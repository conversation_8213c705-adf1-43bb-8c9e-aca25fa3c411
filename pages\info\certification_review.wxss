/* pages/info/certification_review.wxss */

/* 页面样式 */
page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: fixed;
  -webkit-overflow-scrolling: touch;
  background-color: #F5F5F5;
}

.container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
}

/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
  width: 60rpx;
  height: 44px;
  z-index: 10;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 主内容区域 */
.main-content {
  width: 100%;
  min-height: calc(100vh - 44px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background: linear-gradient(135deg, #DCECFF 0%, #EAECF6 25%, #FFECE8 50%, #DCECFF 75%, #F1F4F9 100%);
  padding: 30rpx;
  box-sizing: border-box;
}

/* 认证状态卡片 */
.status-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}

.status-icon {
  margin-right: 30rpx;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

/* 通用区域样式 */
.progress-section,
.info-section,
.tips-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}

/* 审核进度样式 */
.progress-list {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.progress-item {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding-bottom: 40rpx;
}

.progress-item:last-child {
  padding-bottom: 0;
}

.progress-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20rpx;
  top: 40rpx;
  width: 2rpx;
  height: 40rpx;
  background: #e0e0e0;
}

.progress-item.completed::after {
  background: #4080ff;
}

.progress-item.active::after {
  background: linear-gradient(to bottom, #4080ff 50%, #e0e0e0 50%);
}

.progress-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #e0e0e0;
  margin-right: 30rpx;
  flex-shrink: 0;
  position: relative;
}

.progress-item.completed .progress-dot {
  background: #4080ff;
}

.progress-item.completed .progress-dot::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.progress-item.active .progress-dot {
  background: #FF9500;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 149, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 20rpx rgba(255, 149, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 149, 0, 0);
  }
}

.progress-content {
  flex: 1;
}

.progress-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.progress-time {
  font-size: 24rpx;
  color: #999;
}

/* 资料信息卡片 */
.info-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.info-row {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
  word-break: break-all;
}

/* 温馨提示卡片 */
.tips-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item van-icon {
  margin-right: 15rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

/* 联系客服区域 */
.contact-section {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.contact-btn {
  width: 100% !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  background-color: #4080ff !important;
  color: #fff !important;
  font-size: 30rpx !important;
  border-radius: 24rpx !important;
  border: none !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4rpx 20rpx rgba(64, 128, 255, 0.3) !important;
}

.contact-btn::after {
  border: none !important;
}

.contact-btn van-icon {
  margin-right: 15rpx;
}

.contact-btn:active {
  background-color: #3366cc !important;
  transform: translateY(1rpx);
}

/* 确保内容区域在 iOS 底部安全区域上方 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
  .main-content {
    padding-bottom: calc(30rpx + constant(safe-area-inset-bottom));
  }
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .main-content {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
}
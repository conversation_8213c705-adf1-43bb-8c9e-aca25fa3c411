// pages/info/certification_review.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 关注微信公众号
   */
  followWechat() {
    wx.showModal({
      title: '关注公众号',
      content: '请在微信中搜索"找车侠"公众号并关注，输入"审核"获取最新认证动态',
      showCancel: true,
      cancelText: '取消',
      confirmText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 可以在这里添加复制公众号名称到剪贴板的功能
          wx.setClipboardData({
            data: '找车侠',
            success: () => {
              wx.showToast({
                title: '公众号名称已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001234567',
            fail: () => {
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 模拟刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '已是最新状态',
        icon: 'none'
      });
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '企业认证中',
      path: '/pages/info/certification_review'
    };
  }
})
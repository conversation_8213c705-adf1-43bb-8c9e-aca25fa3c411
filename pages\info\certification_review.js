// pages/info/certification_review.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,
    submitTime: '2024-01-15 10:30',
    companyName: '',
    creditCode: '',
    legalPerson: '',
    contactPhone: '',
    companyAddress: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 模拟获取认证信息
    this.loadCertificationInfo();
  },

  /**
   * 加载认证信息
   */
  loadCertificationInfo() {
    // 这里应该从后端获取认证信息
    // 模拟数据
    this.setData({
      companyName: '示例汽车贸易有限公司',
      creditCode: '91110000123456789X',
      legalPerson: '张三',
      contactPhone: '138****8888',
      companyAddress: '北京市朝阳区示例街道123号',
      submitTime: '2024-01-15 10:30'
    });
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001234567',
            fail: (err) => {
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新认证信息
    this.loadCertificationInfo();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '企业认证中',
      path: '/pages/info/certification_review'
    };
  }
})
{"pages": ["pages/index/index", "pages/video/detail", "pages/video/index", "pages/member/recharge_record", "pages/train_operation/index", "pages/info/index", "pages/information/index", "pages/article/edit", "pages/my/quote_record", "pages/favorite/index", "pages/my/quote_list", "pages/moments/index", "pages/moments/detail", "pages/vehicle/index", "pages/vehicle/bdetail", "pages/vehicle/ndetail", "pages/buy/index", "pages/my/index", "pages/search/index", "pages/buy/detail", "pages/login/index", "pages/info/certification", "pages/agreement/service", "pages/agreement/privacy", "pages/moments/release", "pages/my/quote", "pages/staff/select", "pages/staff/add", "pages/staff/edit", "pages/car/select", "pages/car/series", "pages/car/model", "pages/commercial/index", "pages/commercial/detail", "pages/evaluate/index", "pages/evaluate/result", "pages/article/index", "pages/article/draft", "pages/set/index", "pages/set/revise", "pages/set/name", "pages/set/phonein", "pages/set/phoneget", "pages/feedback/index", "pages/vehicle/params", "pages/member/index", "pages/member/pay", "pages/member/renewal", "pages/train_operation/detais", "pages/brand/brand", "pages/train_operation/contract", "pages/train_operation/record", "pages/train_operation/appendix", "pages/login/login_form", "pages/login/register_form", "pages/login/forgot_password", "pages/financialservices/financialservices", "pages/financialservices/financialservicesInfo", "pages/supply_chain_services/index", "pages/flashing_machine/flashing_machine", "pages/flashing_machine/flashing_machine_info", "pages/testing_services/index", "pages/testing_services/info", "pages/domestic_trailer/domestic_trailer", "pages/domestic_trailer/domestic_trailer_info", "pages/foreign_logistics/foreign_logistics", "pages/foreign_logistics/foreign_logistics_info", "pages/supply_chain_services/channel_service", "pages/supply_chain_services/car_service", "pages/supply_chain_services/channel_service_detail", "pages/supply_chain_services/car_service_detail", "pages/my/order", "pages/info/certification_review"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "找车侠", "navigationBarTextStyle": "black"}, "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "tabBar": {"custom": false, "color": "#999999", "selectedColor": "#1296db", "backgroundColor": "#ffffff", "borderStyle": "black", "fontSize": "16px", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "icons/home.png", "selectedIconPath": "icons/home-selected.png"}, {"pagePath": "pages/buy/index", "text": "找车", "iconPath": "icons/buy.png", "selectedIconPath": "icons/buy-selected.png"}, {"pagePath": "pages/moments/index", "text": "询盘", "iconPath": "icons/moments.png", "selectedIconPath": "icons/moments-selected.png"}, {"pagePath": "pages/my/index", "text": "我的", "iconPath": "icons/my.png", "selectedIconPath": "icons/my-selected.png"}]}, "usingComponents": {"search-bar": "/components/search-bar/search-bar", "nav-grid": "/components/nav-grid/nav-grid", "car-list": "/components/car-list/car-list", "van-area": "@vant/weapp/area/index", "van-button": "@vant/weapp/button/index", "van-popup": "@vant/weapp/popup/index", "van-slider": "@vant/weapp/slider/index", "van-icon": "@vant/weapp/icon/index", "van-overlay": "@vant/weapp/overlay/index"}, "lazyCodeLoading": "requiredComponents"}